# 开发环境配置
VITE_NODE_ENV=development
VITE_USE_MOCK=true
VITE_API_URL=http://localhost:3000

# 项目基础配置
VITE_APP_TITLE=React Admin Dev
VITE_APP_DESC=React Admin 开发环境

# 服务配置
VITE_PORT=5173
VITE_PROXY=true

# API配置
VITE_API_PREFIX=/api
VITE_UPLOAD_URL=http://localhost:3000/upload
VITE_WS_URL=ws://localhost:3000/ws

# 功能配置
VITE_USE_PWA=false
VITE_USE_HTTPS=false

# 调试配置
VITE_DEV_TOOLS=true
VITE_DROP_CONSOLE=false
VITE_DROP_DEBUGGER=false 